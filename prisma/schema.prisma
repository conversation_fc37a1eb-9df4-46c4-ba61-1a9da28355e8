generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model UserLocation {
  id             String   @id @default(cuid())
  userId         String   @unique
  latitude       Float? // 纬度
  longitude      Float? // 经度
  lastUpdate     DateTime @db.DateTime(0)
  lastOnlineTime DateTime @db.DateTime(0)
  user           User     @relation(fields: [userId], references: [userId])

  @@index([userId], map: "UserLocation_userId_fkey")
}

model User {
  id                       String             @id @default(cuid())
  userId                   String             @unique
  passwordHash             String?
  nickname                 String
  avatar                   String?
  avatarURL                String? // 使用COS的URL存储
  lastActiveTime           DateTime?          @db.DateTime(0)
  createdAt                DateTime?          @db.DateTime(0)
  friendships              Friendship[]       @relation("UserFriendships")
  locations                UserLocation[]
  authoredCards            ItemCard[]         @relation("AuthoredCards") // 用户创建的卡片（作为作者）
  heldCards                UserItemCard[] // 用户持有的卡片（创建的 + 接收的）
  sharingLocation          Boolean            @default(true) // 用户可以选择是否共享位置，默认开启
  carbonCoins              Int                @default(0) // 用户的碳币数量
  sentTransfers            ItemCardTransfer[] @relation("SenderTransfers")
  receivedTransfers        ItemCardTransfer[] @relation("ReceiverTransfers")
  ItemCard                 ItemCard[]
  footprints               UserFootprints[] // 用户的足迹记录
  locationCheckIns         LocationCheckIns[] // 用户的地点打卡记录
  userLogs                 UserLogs[] // 用户的日志记录
  likes                    Likes[] // 用户的点赞记录
  comments                 RecordComments[] // 用户的评论记录
  sentPropInteractions     PropInteraction[]  @relation("SenderPropInteractions") // 发送的道具交互
  receivedPropInteractions PropInteraction[]  @relation("ReceiverPropInteractions") // 接收的道具交互
}

model Friendship {
  id        String           @id @default(cuid())
  userId    String
  friendId  String
  status    FriendshipStatus
  createdAt DateTime         @db.DateTime(0)
  updatedAt DateTime         @db.DateTime(0)
  user      User             @relation("UserFriendships", fields: [userId], references: [userId])

  @@index([userId], map: "Friendship_userId_fkey")
}

enum FriendshipStatus {
  pending
  accepted
  rejected
}

enum TransferStatus {
  pending
  accepted
  rejected
}

enum ActivityType {
  walking
  cycling
  bus
  subway
}

enum RecordType {
  location
  trip
  recognition
}

// 创建的唯一索引
model ItemCard {
  id            String             @id @default(cuid())
  tags          Json // 存储标签数组
  description   String             @db.Text
  title         String
  imageFileName String // 本地存储的文件名
  imageURL      String // COS返回的URL，供共享使用
  createdAt     DateTime           @default(now()) @db.DateTime(0)
  authorId      String // 存储创建这个卡片的用户id
  location      String // 位置字符串，用于显示
  latitude      Float? // 纬度
  longitude     Float? // 经度
  author        User               @relation("AuthoredCards", fields: [authorId], references: [userId])
  transfers     ItemCardTransfer[] // 卡片的传输记录
  holders       UserItemCard[] // 持有此卡片的用户关联
  User          User?              @relation(fields: [userId], references: [id])
  userId        String?
  UserLogs      UserLogs[]

  @@index([authorId], map: "ItemCard_authorId_fkey")
  @@index([createdAt], map: "ItemCard_createdAt_idx")
}

model ItemCardTransfer {
  id           String         @id @default(cuid())
  senderId     String
  receiverId   String
  cardId       String
  transferTime DateTime       @default(now()) @db.DateTime(0)
  status       TransferStatus @default(pending)
  sender       User           @relation("SenderTransfers", fields: [senderId], references: [userId])
  receiver     User           @relation("ReceiverTransfers", fields: [receiverId], references: [userId])
  card         ItemCard       @relation(fields: [cardId], references: [id])

  @@index([senderId], map: "ItemCardTransfer_senderId_fkey")
  @@index([receiverId], map: "ItemCardTransfer_receiverId_fkey")
  @@index([cardId], map: "ItemCardTransfer_cardId_fkey")
  @@index([transferTime], map: "ItemCardTransfer_transferTime_idx")
}

model UserItemCard {
  id         String   @id @default(cuid())
  userId     String
  cardId     String
  remark     String?  @db.Text // 用户独立的备注（个性化）
  acquiredAt DateTime @default(now()) @db.DateTime(0) // 获取时间（创建或接受传输时）
  isOwner    Boolean  @default(false) // 是否是作者（true 为作者，便于区分编辑权限）
  user       User     @relation(fields: [userId], references: [userId])
  card       ItemCard @relation(fields: [cardId], references: [id])

  @@unique([userId, cardId]) // 防止用户重复持有同一卡片
  @@index([userId], map: "UserItemCard_userId_fkey")
  @@index([cardId], map: "UserItemCard_cardId_fkey")
  @@index([acquiredAt], map: "UserItemCard_acquiredAt_idx")
}

model UserFootprints {
  id            String       @id @default(cuid())
  userId        String
  user          User         @relation(fields: [userId], references: [userId])
  footPrints    Json // JSON 数组，包含 latitude, longitude, timestamp
  activityType  ActivityType // 出行活动类型
  isFinished    Boolean // 是否已完成此次出行
  totalDistance Float        @default(0) // 总距离（公里）
  createdAt     DateTime     @default(now()) @db.DateTime(0)
  updatedAt     DateTime     @updatedAt @db.DateTime(0)
  UserLogs      UserLogs[]

  @@index([userId], map: "UserFootprints_userId_fkey")
  @@index([createdAt], map: "UserFootprints_createdAt_idx")
}

model LocationCheckIns {
  id        String     @id @default(cuid())
  userId    String
  user      User       @relation(fields: [userId], references: [userId])
  position  String? // 地点名称，如"星巴克"
  latitude  Float // 纬度
  longitude Float // 经度
  createdAt DateTime   @default(now()) @db.DateTime(0)
  updatedAt DateTime   @updatedAt @db.DateTime(0)
  userLogs  UserLogs[] // 关联的日志记录

  @@index([userId], map: "LocationCheckIns_userId_fkey")
  @@index([createdAt], map: "LocationCheckIns_createdAt_idx")
}

model UserLogs {
  id                 String            @id @default(cuid())
  userId             String
  user               User              @relation(fields: [userId], references: [userId])
  recordType         RecordType // 记录类型：location, trip, recognition
  recordId           String // 关联记录的ID
  imageList          Json? // 图片URL数组
  description        String? // 用户描述
  isPublic           Boolean           @default(false) // 是否向好友开放
  likedBy            Json              @default("[]") // 点赞用户ID数组
  commentsId         Json              @default("[]") // 评论ID数组
  createdAt          DateTime          @default(now()) @db.DateTime(0)
  updatedAt          DateTime          @updatedAt @db.DateTime(0)
  likes              Likes[] // 关联的点赞记录
  comments           RecordComments[] // 关联的评论记录
  LocationCheckIns   LocationCheckIns? @relation(fields: [locationCheckInsId], references: [id])
  locationCheckInsId String?
  ItemCard           ItemCard?         @relation(fields: [itemCardId], references: [id])
  itemCardId         String?
  UserFootprints     UserFootprints?   @relation(fields: [userFootprintsId], references: [id])
  userFootprintsId   String?

  @@index([userId], map: "UserLogs_userId_fkey")
  @@index([recordType], map: "UserLogs_recordType_idx")
  @@index([createdAt], map: "UserLogs_createdAt_idx")
  @@index([recordId], map: "UserLogs_recordId_idx")
}

model Likes {
  id        String   @id @default(cuid())
  logId     String
  userId    String
  user      User     @relation(fields: [userId], references: [userId])
  userLog   UserLogs @relation(fields: [logId], references: [id], onDelete: Cascade)
  createdAt DateTime @default(now()) @db.DateTime(0)

  @@unique([logId, userId]) // 防止重复点赞
  @@index([logId], map: "Likes_logId_fkey")
  @@index([userId], map: "Likes_userId_fkey")
}

model RecordComments {
  id        String   @id @default(cuid())
  logId     String
  replyTo   String? // 回复的评论ID
  userId    String
  content   String   @db.Text
  user      User     @relation(fields: [userId], references: [userId])
  userLog   UserLogs @relation(fields: [logId], references: [id], onDelete: Cascade)
  createdAt DateTime @default(now()) @db.DateTime(0)

  @@index([logId], map: "RecordComments_logId_fkey")
  @@index([userId], map: "RecordComments_userId_fkey")
  @@index([replyTo], map: "RecordComments_replyTo_idx")
}

// 道具模型
model Prop {
  id           String            @id @default(cuid())
  name         String // 道具名称
  description  String?           @db.Text // 道具描述
  imageURL     String? // 道具图片URL
  propType     String // 道具类型（如：gift, sticker, emoji等）
  value        Int               @default(0) // 道具价值（碳币）
  isActive     Boolean           @default(true) // 是否可用
  createdAt    DateTime          @default(now()) @db.DateTime(0)
  updatedAt    DateTime          @updatedAt @db.DateTime(0)
  interactions PropInteraction[] // 关联的交互记录

  @@index([propType], map: "Prop_propType_idx")
  @@index([isActive], map: "Prop_isActive_idx")
}

// 道具交互模型
model PropInteraction {
  id              String    @id @default(cuid())
  senderUserId    String // 发送道具的用户ID（用户A）
  receiverUserId  String // 接收道具的用户ID（用户B）
  propId          String // 道具ID，关联到Prop模型
  interactionTime DateTime  @default(now()) @db.DateTime(0) // 交互发生时间
  receivedTime    DateTime? // 接收时间（触发动画的时间）
  isRead          Boolean   @default(false) // 用户B是否已查看
  sender          User      @relation("SenderPropInteractions", fields: [senderUserId], references: [userId])
  receiver        User      @relation("ReceiverPropInteractions", fields: [receiverUserId], references: [userId])
  prop            Prop      @relation(fields: [propId], references: [id])

  @@index([senderUserId], map: "PropInteraction_senderUserId_fkey")
  @@index([receiverUserId], map: "PropInteraction_receiverUserId_fkey")
  @@index([propId], map: "PropInteraction_propId_fkey")
  @@index([interactionTime], map: "PropInteraction_interactionTime_idx")
  @@index([isRead], map: "PropInteraction_isRead_idx")
}

import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

// 创建道具交互记录
export async function POST(req: NextRequest) {
  try {
    const { senderUserId, receiverUserId, propId } = await req.json();

    // 参数验证
    if (!senderUserId || !receiverUserId || propId === undefined) {
      return NextResponse.json(
        { error: "缺少必要参数：senderUserId, receiverUserId, propId" },
        { status: 400 }
      );
    }

    if (senderUserId === receiverUserId) {
      return NextResponse.json(
        { error: "不能向自己发送道具" },
        { status: 400 }
      );
    }

    // 验证发送者存在
    const sender = await prisma.user.findUnique({
      where: { userId: senderUserId },
    });

    if (!sender) {
      return NextResponse.json({ error: "发送者不存在" }, { status: 404 });
    }

    // 验证接收者存在
    const receiver = await prisma.user.findUnique({
      where: { userId: receiverUserId },
    });

    if (!receiver) {
      return NextResponse.json({ error: "接收者不存在" }, { status: 404 });
    }

    // 创建道具交互记录
    const propInteraction = await prisma.propInteraction.create({
      data: {
        senderUserId,
        receiverUserId,
        propId: parseInt(propId),
      },
      include: {
        sender: {
          select: {
            userId: true,
            nickname: true,
            avatarURL: true,
          },
        },
        receiver: {
          select: {
            userId: true,
            nickname: true,
            avatarURL: true,
          },
        },
      },
    });

    return NextResponse.json({
      success: true,
      message: "道具交互创建成功",
      data: propInteraction,
    });
  } catch (error) {
    console.error("创建道具交互失败:", error);
    return NextResponse.json(
      { success: false, message: "服务器内部错误" },
      { status: 500 }
    );
  }
}

// 撤回交互（仅限对方未读状态，直接删除记录）
export async function DELETE(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const interactionId = searchParams.get("interactionId");
    const userId = searchParams.get("userId");

    if (!interactionId || !userId) {
      return NextResponse.json(
        { error: "缺少interactionId或userId参数" },
        { status: 400 }
      );
    }

    // 查找交互记录
    const interaction = await prisma.propInteraction.findUnique({
      where: { id: interactionId },
    });

    if (!interaction) {
      return NextResponse.json({ error: "交互记录不存在" }, { status: 404 });
    }

    // 验证是否是发送者
    if (interaction.senderUserId !== userId) {
      return NextResponse.json(
        { error: "只有发送者可以撤回交互" },
        { status: 403 }
      );
    }

    // 验证对方是否已读
    if (interaction.isRead) {
      return NextResponse.json(
        { error: "对方已查看，无法撤回" },
        { status: 400 }
      );
    }

    // 删除交互记录
    await prisma.propInteraction.delete({
      where: { id: interactionId },
    });

    return NextResponse.json({
      success: true,
      message: "交互撤回成功",
    });
  } catch (error) {
    console.error("撤回交互失败:", error);
    return NextResponse.json(
      { success: false, message: "服务器内部错误" },
      { status: 500 }
    );
  }
}

// 修改已读状态（需验证传入的userId是否等于接收者的id）
export async function PATCH(req: NextRequest) {
  try {
    const { interactionId, userId, isRead, receivedTime } = await req.json();

    // 参数验证
    if (!interactionId || !userId || isRead === undefined) {
      return NextResponse.json(
        { error: "缺少必要参数：interactionId, userId, isRead" },
        { status: 400 }
      );
    }

    // 查找交互记录
    const interaction = await prisma.propInteraction.findUnique({
      where: { id: interactionId },
    });

    if (!interaction) {
      return NextResponse.json({ error: "交互记录不存在" }, { status: 404 });
    }

    // 验证是否是接收者
    if (interaction.receiverUserId !== userId) {
      return NextResponse.json(
        { error: "只有接收者可以修改已读状态" },
        { status: 403 }
      );
    }

    // 更新已读状态
    const updateData: any = { isRead };

    // 如果提供了接收时间，则更新接收时间
    if (receivedTime) {
      updateData.receivedTime = new Date(receivedTime);
    } else {
      // 如果标记为已读且之前没有接收时间，则设置当前时间
      updateData.receivedTime = new Date();
    }

    const updatedInteraction = await prisma.propInteraction.update({
      where: { id: interactionId },
      data: updateData,
      include: {
        sender: {
          select: {
            userId: true,
            nickname: true,
            avatarURL: true,
          },
        },
        receiver: {
          select: {
            userId: true,
            nickname: true,
            avatarURL: true,
          },
        },
      },
    });

    return NextResponse.json({
      success: true,
      message: "已读状态更新成功",
      data: updatedInteraction,
    });
  } catch (error) {
    console.error("更新已读状态失败:", error);
    return NextResponse.json(
      { success: false, message: "服务器内部错误" },
      { status: 500 }
    );
  }
}

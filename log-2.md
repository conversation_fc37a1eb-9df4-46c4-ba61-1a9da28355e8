# 道具交互系统 API 文档

## 概述

道具交互系统允许用户之间发送虚拟道具，支持创建交互、撤回交互、修改已读状态等功能。

---

## API 接口列表

### 1. 创建道具交互

- **接口路径**: `POST /api/prop-interaction`
- **功能**: 创建新的道具交互记录
- **测试性 Curl：**

```bash
curl -i -X POST "http://*************:3000/api/prop-interaction" \
  -H "content-type: application/json" \
  -d '{"senderUserId":"alice","receiverUserId":"bob","propId":1}'
```

- **请求参数**:

  ```typescript
  // Body 参数
  {
    senderUserId: string; // 发送道具的用户ID
    receiverUserId: string; // 接收道具的用户ID
    propId: number; // 道具ID，前端用于调用本地动画
  }
  ```

- **响应数据**:

  ```typescript
  // 成功响应
  {
    success: true;
    message: "道具交互创建成功";
    data: {
      id: string;
      senderUserId: string;
      receiverUserId: string;
      propId: number;
      interactionTime: Date;
      receivedTime: Date | null;
      isRead: boolean;
      sender: {
        userId: string;
        nickname: string;
        avatarURL: string | null;
      }
      receiver: {
        userId: string;
        nickname: string;
        avatarURL: string | null;
      }
    }
  }
  ```

- **状态码**:

  - `200`: 成功
  - `400`: 参数错误或不能向自己发送道具
  - `404`: 发送者或接收者不存在
  - `500`: 服务器错误

---

### 2. 撤回道具交互

- **接口路径**: `DELETE /api/prop-interaction?interactionId={id}&userId={userId}`
- **功能**: 撤回道具交互（仅限对方未读状态，直接删除记录）
- **测试性 Curl：**

```bash
curl -i -X DELETE "http://*************:3000/api/prop-interaction?interactionId=clxxxxx123&userId=alice"
```

- **请求参数**:

  ```typescript
  // Query 参数
  {
    interactionId: string; // 交互记录ID（String类型）
    userId: string; // 发送者用户ID
  }
  ```

- **响应数据**:

  ```typescript
  // 成功响应
  {
    success: true;
    message: "交互撤回成功";
  }
  ```

- **状态码**:

  - `200`: 成功
  - `400`: 参数错误或对方已查看无法撤回
  - `403`: 只有发送者可以撤回交互
  - `404`: 交互记录不存在
  - `500`: 服务器错误

---

### 3. 修改已读状态

- **接口路径**: `PATCH /api/prop-interaction`
- **功能**: 修改道具交互的已读状态（需验证传入的 userId 是否等于接收者的 id）
- **测试性 Curl：**

```bash
curl -i -X PATCH "http://*************:3000/api/prop-interaction" \
  -H "content-type: application/json" \
  -d '{"interactionId":"clxxxxx123","userId":"bob","isRead":true}'
```

- **请求参数**:

  ```typescript
  // Body 参数
  {
    interactionId: string; // 交互记录ID（String类型）
    userId: string; // 接收者用户ID
    isRead: boolean; // 是否已读
    receivedTime?: string; // 可选：接收时间（ISO字符串）
  }
  ```

- **响应数据**:

  ```typescript
  // 成功响应
  {
    success: true;
    message: "已读状态更新成功";
    data: {
      id: string;
      senderUserId: string;
      receiverUserId: string;
      propId: number;
      interactionTime: Date;
      receivedTime: Date | null;
      isRead: boolean;
      sender: {
        userId: string;
        nickname: string;
        avatarURL: string | null;
      }
      receiver: {
        userId: string;
        nickname: string;
        avatarURL: string | null;
      }
    }
  }
  ```

- **状态码**:

  - `200`: 成功
  - `400`: 参数错误
  - `403`: 只有接收者可以修改已读状态
  - `404`: 交互记录不存在
  - `500`: 服务器错误

---

### 4. 查询发送统计

- **接口路径**: `GET /api/prop-interaction/stats?userId={userId}`
- **功能**: 查询指定用户发送给其他用户的已读情况，按照已读和未读分组返回
- **测试性 Curl：**

```bash
curl -i -X GET "http://*************:3000/api/prop-interaction/stats?userId=alice"
```

- **请求参数**:

  ```typescript
  // Query 参数
  {
    userId: string; // 发送者用户ID
  }
  ```

- **响应数据**:

  ```typescript
  // 成功响应
  {
    success: true;
    message: "查询成功";
    data: {
      stats: {
        total: number; // 总发送数量
        read: number; // 已读数量
        unread: number; // 未读数量
      }
      readInteractions: Array<{
        id: string;
        senderUserId: string;
        receiverUserId: string;
        propId: number;
        interactionTime: Date;
        receivedTime: Date | null;
        isRead: boolean;
        receiver: {
          userId: string;
          nickname: string;
          avatarURL: string | null;
        };
      }>;
      unreadInteractions: Array<{
        id: string;
        senderUserId: string;
        receiverUserId: string;
        propId: number;
        interactionTime: Date;
        receivedTime: Date | null;
        isRead: boolean;
        receiver: {
          userId: string;
          nickname: string;
          avatarURL: string | null;
        };
      }>;
    }
  }
  ```

- **状态码**:

  - `200`: 成功
  - `400`: 参数错误
  - `404`: 用户不存在
  - `500`: 服务器错误

---

## 数据模型

### PropInteraction 模型

```typescript
{
  id: string; // CUID主键（与项目其他模型保持一致）
  senderUserId: string; // 发送道具的用户ID
  receiverUserId: string; // 接收道具的用户ID
  propId: number; // 道具ID，前端用于调用本地动画
  interactionTime: Date; // 交互发生时间
  receivedTime: Date | null; // 接收时间（触发动画的时间）
  isRead: boolean; // 用户B是否已查看
}
```

## 注意事项

1. **ID 类型**: PropInteraction 模型使用 String 类型的 CUID 主键，与项目中其他模型保持一致
2. **道具信息管理**: 道具信息由前端本地管理，后端只存储 propId 作为标识符
3. **权限验证**:
   - 只有发送者可以撤回交互（且仅限对方未读状态）
   - 只有接收者可以修改已读状态
4. **时间处理**:
   - 如果标记为已读且之前没有接收时间，系统会自动设置当前时间
   - 可以手动指定接收时间
5. **数据一致性**: 所有操作都包含完整的参数验证和错误处理
